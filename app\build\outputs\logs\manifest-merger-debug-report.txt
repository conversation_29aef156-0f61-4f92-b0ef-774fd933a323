-- Merging decision tree log ---
manifest
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af63541532fa803e56e4220f2422797e\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a72393f873e908adb2a0264038c808dd\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb90d7667b587f80ad4a590f09d4c109\transformed\hilt-android-2.51.1\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac8e8bbf722257eb68be5834964871a\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fff549d7b1e992263fe93e7e4ad84fd3\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\2deff07ff90b3b3c74a28fc2db9f5852\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de09855bd61fa252d63075166b306596\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89da252ff4a6a2b1f71aa9984a54919\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83ec4bf2383211914d027d2178979b92\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17c318d176747f1c9c864fc71aa14b29\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80dfd7d22229502377a8e35462845a89\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd6f1ec69cb0662412920ad94508cc89\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad7ab59ee09f4bce2e51570ac967d966\transformed\ui-tooling-data\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\46091396fb1d3c60c19f3095a347b04d\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83d7276adb04464c65931a5ac9e7f64b\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d1648d94f977bfeeb970b03951b0838\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b18247d773b7df6889ab954d02f2eb2\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6664a02ba9865c3a18914cdd2bea3df2\transformed\foundation-layout\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db58a601a5e236f116f2543f658d7224\transformed\animation-core\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51827f3b8989e1fbc19b6f70d1ca96d9\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9490f247864ecf611804f7380525087e\transformed\ui-util\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3394fee6f388bfb62d59251ba6830788\transformed\ui-unit\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f212d73effad6b49c600a8e901b19137\transformed\ui-text\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06eb6d2f4b54d35f8188012265af67e1\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1722c3a48fb7870bcbbd5469fcf71460\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\372494e3e1187b8ef30a2dee39125796\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e86da91887b9319930073cbef794c74\transformed\lifecycle-livedata-core-ktx-2.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\625aa0215b900b2cd769cb30f6d1fe78\transformed\lifecycle-viewmodel-ktx-2.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\929b6df054feab43d71e2da0169a8dbb\transformed\lifecycle-livedata-2.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\74e02b6a3b4ff80bed4c43f861f89116\transformed\runtime-annotation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f97fe2bf5087a01c6545e0c9795afde5\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f530796092af4402355a3f59186f856\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b5fcea9bdabe11f103403154cc2780\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5470438d8563ebc989c0f135317124b\transformed\runtime\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ab01fa2dc9f93f355acbafdd4499905\transformed\runtime-saveable\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1350b4467826a434dab02f1100b69694\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\98987a4505bfcddaab76c1a78b34cb88\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\dad8bc95f9bfed8e0655b9236ee1e692\transformed\lifecycle-viewmodel-2.9.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c418d75ea5be54311f1c76fed18e630\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\000d36e943d774f8beab659593bc2726\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5484553a6637e9af9aa0ce759e1f860c\transformed\lifecycle-livedata-core-2.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb69bbf327b71042f98f38aa7d02aff7\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\007ecb535472055e7150777697eabb1a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb86971568f85badfe22c42ac26d33ec\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\090be5d6fafb061bae3b6e996ff571b4\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db402df5dabe119c5da50ff02986a2c4\transformed\activity-ktx-1.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b3fa52ff9069c70a089af782a634aca\transformed\activity-1.11.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15057394e7aa5be13f4b45d11262b9da\transformed\activity-compose-1.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6760d9b14ae2136c5d362bb380ff18c3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d6756dc21f7164bc9904b76f272c7ae\transformed\core-ktx-1.17.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1da2b7a9fa92d96988c8c3fb269f37fd\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcfeaf7e3406927b142423354a1fef43\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f329b1977ea78544ebc9c5bdf729ac29\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8a9443c33e8a021aaf6971aa091021d\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e2960fb4561aecb4cc2e96a3d7b8c76\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ec6ce608e3a826f1241cb4f6ab79344\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9603ba79bfff51ad93f5076ad9de65f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13de6f7c29989995feb8c88e5c0abe2a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\632d35a815226d24d2793c29a342d883\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\327ba4983396579f7669aa9665000479\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc070acf84ca42179602c8f0498b1a86\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\412d356aabc8871bc0fe68567fbc36dd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a216c5df336d1d8dad2031132becefe1\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:5:22-64
application
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:7:5-28:19
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:7:5-28:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\412d356aabc8871bc0fe68567fbc36dd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\412d356aabc8871bc0fe68567fbc36dd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:15:9-35
	android:label
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:13:9-41
	android:fullBackupContent
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:11:9-54
	android:roundIcon
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:14:9-54
	android:icon
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:12:9-43
	android:allowBackup
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:16:9-48
	android:dataExtractionRules
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:10:9-65
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:8:9-30
activity#com.example.sampletest.MainActivity
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:17:9-27:20
	android:label
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:20:13-45
	android:exported
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:19:13-36
	android:theme
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:21:13-52
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:18:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:22:13-26:29
action#android.intent.action.MAIN
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:23:17-69
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:23:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:25:17-77
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:25:27-74
uses-sdk
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af63541532fa803e56e4220f2422797e\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af63541532fa803e56e4220f2422797e\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a72393f873e908adb2a0264038c808dd\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a72393f873e908adb2a0264038c808dd\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb90d7667b587f80ad4a590f09d4c109\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb90d7667b587f80ad4a590f09d4c109\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-runtime-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac8e8bbf722257eb68be5834964871a\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac8e8bbf722257eb68be5834964871a\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fff549d7b1e992263fe93e7e4ad84fd3\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fff549d7b1e992263fe93e7e4ad84fd3\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\2deff07ff90b3b3c74a28fc2db9f5852\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\2deff07ff90b3b3c74a28fc2db9f5852\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de09855bd61fa252d63075166b306596\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de09855bd61fa252d63075166b306596\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89da252ff4a6a2b1f71aa9984a54919\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89da252ff4a6a2b1f71aa9984a54919\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83ec4bf2383211914d027d2178979b92\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83ec4bf2383211914d027d2178979b92\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17c318d176747f1c9c864fc71aa14b29\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17c318d176747f1c9c864fc71aa14b29\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80dfd7d22229502377a8e35462845a89\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80dfd7d22229502377a8e35462845a89\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd6f1ec69cb0662412920ad94508cc89\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd6f1ec69cb0662412920ad94508cc89\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad7ab59ee09f4bce2e51570ac967d966\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad7ab59ee09f4bce2e51570ac967d966\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\46091396fb1d3c60c19f3095a347b04d\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\46091396fb1d3c60c19f3095a347b04d\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83d7276adb04464c65931a5ac9e7f64b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83d7276adb04464c65931a5ac9e7f64b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d1648d94f977bfeeb970b03951b0838\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d1648d94f977bfeeb970b03951b0838\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b18247d773b7df6889ab954d02f2eb2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b18247d773b7df6889ab954d02f2eb2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6664a02ba9865c3a18914cdd2bea3df2\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6664a02ba9865c3a18914cdd2bea3df2\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db58a601a5e236f116f2543f658d7224\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db58a601a5e236f116f2543f658d7224\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51827f3b8989e1fbc19b6f70d1ca96d9\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51827f3b8989e1fbc19b6f70d1ca96d9\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9490f247864ecf611804f7380525087e\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9490f247864ecf611804f7380525087e\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3394fee6f388bfb62d59251ba6830788\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3394fee6f388bfb62d59251ba6830788\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f212d73effad6b49c600a8e901b19137\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f212d73effad6b49c600a8e901b19137\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06eb6d2f4b54d35f8188012265af67e1\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06eb6d2f4b54d35f8188012265af67e1\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1722c3a48fb7870bcbbd5469fcf71460\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1722c3a48fb7870bcbbd5469fcf71460\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\372494e3e1187b8ef30a2dee39125796\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\372494e3e1187b8ef30a2dee39125796\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e86da91887b9319930073cbef794c74\transformed\lifecycle-livedata-core-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e86da91887b9319930073cbef794c74\transformed\lifecycle-livedata-core-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\625aa0215b900b2cd769cb30f6d1fe78\transformed\lifecycle-viewmodel-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\625aa0215b900b2cd769cb30f6d1fe78\transformed\lifecycle-viewmodel-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\929b6df054feab43d71e2da0169a8dbb\transformed\lifecycle-livedata-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\929b6df054feab43d71e2da0169a8dbb\transformed\lifecycle-livedata-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\74e02b6a3b4ff80bed4c43f861f89116\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\74e02b6a3b4ff80bed4c43f861f89116\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f97fe2bf5087a01c6545e0c9795afde5\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f97fe2bf5087a01c6545e0c9795afde5\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f530796092af4402355a3f59186f856\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f530796092af4402355a3f59186f856\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b5fcea9bdabe11f103403154cc2780\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b5fcea9bdabe11f103403154cc2780\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5470438d8563ebc989c0f135317124b\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5470438d8563ebc989c0f135317124b\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ab01fa2dc9f93f355acbafdd4499905\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ab01fa2dc9f93f355acbafdd4499905\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1350b4467826a434dab02f1100b69694\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1350b4467826a434dab02f1100b69694\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\98987a4505bfcddaab76c1a78b34cb88\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\98987a4505bfcddaab76c1a78b34cb88\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\dad8bc95f9bfed8e0655b9236ee1e692\transformed\lifecycle-viewmodel-2.9.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\dad8bc95f9bfed8e0655b9236ee1e692\transformed\lifecycle-viewmodel-2.9.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c418d75ea5be54311f1c76fed18e630\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c418d75ea5be54311f1c76fed18e630\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\000d36e943d774f8beab659593bc2726\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\000d36e943d774f8beab659593bc2726\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5484553a6637e9af9aa0ce759e1f860c\transformed\lifecycle-livedata-core-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5484553a6637e9af9aa0ce759e1f860c\transformed\lifecycle-livedata-core-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb69bbf327b71042f98f38aa7d02aff7\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb69bbf327b71042f98f38aa7d02aff7\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\007ecb535472055e7150777697eabb1a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\007ecb535472055e7150777697eabb1a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb86971568f85badfe22c42ac26d33ec\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb86971568f85badfe22c42ac26d33ec\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\090be5d6fafb061bae3b6e996ff571b4\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\090be5d6fafb061bae3b6e996ff571b4\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db402df5dabe119c5da50ff02986a2c4\transformed\activity-ktx-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db402df5dabe119c5da50ff02986a2c4\transformed\activity-ktx-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b3fa52ff9069c70a089af782a634aca\transformed\activity-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b3fa52ff9069c70a089af782a634aca\transformed\activity-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15057394e7aa5be13f4b45d11262b9da\transformed\activity-compose-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15057394e7aa5be13f4b45d11262b9da\transformed\activity-compose-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6760d9b14ae2136c5d362bb380ff18c3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6760d9b14ae2136c5d362bb380ff18c3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d6756dc21f7164bc9904b76f272c7ae\transformed\core-ktx-1.17.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d6756dc21f7164bc9904b76f272c7ae\transformed\core-ktx-1.17.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1da2b7a9fa92d96988c8c3fb269f37fd\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1da2b7a9fa92d96988c8c3fb269f37fd\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcfeaf7e3406927b142423354a1fef43\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcfeaf7e3406927b142423354a1fef43\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f329b1977ea78544ebc9c5bdf729ac29\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f329b1977ea78544ebc9c5bdf729ac29\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8a9443c33e8a021aaf6971aa091021d\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8a9443c33e8a021aaf6971aa091021d\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e2960fb4561aecb4cc2e96a3d7b8c76\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e2960fb4561aecb4cc2e96a3d7b8c76\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ec6ce608e3a826f1241cb4f6ab79344\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ec6ce608e3a826f1241cb4f6ab79344\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9603ba79bfff51ad93f5076ad9de65f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9603ba79bfff51ad93f5076ad9de65f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13de6f7c29989995feb8c88e5c0abe2a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13de6f7c29989995feb8c88e5c0abe2a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\632d35a815226d24d2793c29a342d883\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\632d35a815226d24d2793c29a342d883\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\327ba4983396579f7669aa9665000479\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\327ba4983396579f7669aa9665000479\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc070acf84ca42179602c8f0498b1a86\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc070acf84ca42179602c8f0498b1a86\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\412d356aabc8871bc0fe68567fbc36dd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\412d356aabc8871bc0fe68567fbc36dd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a216c5df336d1d8dad2031132becefe1\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a216c5df336d1d8dad2031132becefe1\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57edb1afd5dd314dc242ac8a0fca3ba1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:23:9-81
permission#com.example.sampletest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.sampletest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
