-- Merging decision tree log ---
manifest
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:1-30:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\172f621333dc3168d5076c7988f84bf2\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b790fb9b51a9464d836e63572974ac05\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a47391491fc1ec84f4f0ba2baba0288\transformed\hilt-android-2.51.1\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0655a30f88a218b30140f08f2fcf0f5\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6089407a50a854e03941de03facdaf6\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\832a5e12fc2e9b348024cfc52e216f6d\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d16eb54be87cc3f72afc33985a122320\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0356b8c03d17076269fa25c954e232f\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e4694d548e0bde5f47d9450211c39c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3de91b7448f871adef90601c18d9bac7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab961e0b323c8738ead817fe61c7f0b8\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\57eedf221066f8277e5ccc62329334e3\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\47626705b54ede4907bb1bb15dac0b3d\transformed\ui-tooling-data\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e995c29488c705e757d2dbb2f1ba737\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57cd720ac1eca1884031e0a6e8daf30\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d255fb4602b7bd597f4d7067285752cb\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f907e2130997849173537d35f867bc4c\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2ffef7aab69652caef300e398bf0698\transformed\foundation-layout\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa05b2c24ef13277a477467c689a79a2\transformed\animation-core\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56123d49d534a0872b4085675af9b2df\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be53877d6e443712766bc7362aa0a6a7\transformed\ui-util\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a4f324cab8aff96a85f3dcdfe887bf0\transformed\ui-unit\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d803fbe453431f76308adefa50f600\transformed\ui-text\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88de4aa178c89e467e05d5005edfb3fb\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c936241dc276c958afd9a34321d2a142\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\566720ef7ba90c240bdbe93e9fd63fc8\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ed56cb90dabc9f2e2d9d2136613ed92\transformed\lifecycle-livedata-core-ktx-2.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\00924bf91eb594b39eb8cdc56310da33\transformed\lifecycle-viewmodel-ktx-2.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8457a356bdfd2f36349fce308320bc1\transformed\lifecycle-livedata-2.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9b737eaf88280bea7e98388990cb76\transformed\runtime-annotation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dffb885357fb91405589e6f55004587c\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de488430ac7339ea3295e60325bd15d9\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f8fb1440854fb40ed252f0e17bff4f0\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f590eaf319c20c8360e88a8e9e93197f\transformed\runtime\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc055364240d6a8f1965da7f58bd3942\transformed\runtime-saveable\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2b9b93b87687c4c7d73c7dd525a92e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\17125649c9a2e9297cc7eff77eda4bb1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\42079e344a0b5d170d617454d1de27ff\transformed\lifecycle-viewmodel-2.9.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\395ca52991126ad48466f9663dd12c85\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\04f856bfa19d0797e812242b358c314e\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5661e7ae791ba8e41f4a59a0575d81c9\transformed\lifecycle-livedata-core-2.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\38b911cdb4aff11ef71c27b92218e01e\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7f0597caab3d480fa1c8e4718aa77f4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9236191ebbd56371186526fe7595f2a2\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\391a5066ef22ceff677de3e645b947ad\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547c9f48917163359dd028d00faaea5d\transformed\activity-ktx-1.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ee85b90936406b9aa2c433fd622ab93\transformed\activity-1.11.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23dfb108c822dd0b8a4d0a6fa594f3ee\transformed\activity-compose-1.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb19184049a54f3cf914e69635db97d7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad8c275ad12fe7edf1c4e52f6a3a87a2\transformed\core-ktx-1.17.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5584ef833df34b2c448068d6a33d2e9c\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\549cf5e4880f4db2dab2a693ba979d58\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12741c8546b01cf589ebbd2ad2926dbe\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eedbb90067f8043472c2f37c4e4f010\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cae152fce28a8a82bb94ab95a2acb8c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5279eacb29d2014c1c50d46d56630da\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f174e659b91022016360435562b6416\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0b4bf2e207b8393423e4476ab8f1a32\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b00696eb8b4ee2faccdc004cf055301\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e39619202ef2ac0ac8dcb893726a0142\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1301c549d9f105af2e7063d2b5562e4\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80ca90e0f0e3f96cb6a4b25520c15249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c651c0fcef1e85380bbfecffa5db8690\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:5:22-64
application
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:7:5-28:19
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:7:5-28:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80ca90e0f0e3f96cb6a4b25520c15249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80ca90e0f0e3f96cb6a4b25520c15249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:15:9-35
	android:label
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:13:9-41
	android:fullBackupContent
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:11:9-54
	android:roundIcon
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:14:9-54
	android:icon
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:12:9-43
	android:allowBackup
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:16:9-48
	android:dataExtractionRules
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:10:9-65
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:8:9-30
activity#com.example.sampletest.MainActivity
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:17:9-27:20
	android:label
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:20:13-45
	android:exported
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:19:13-36
	android:theme
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:21:13-52
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:18:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:22:13-26:29
action#android.intent.action.MAIN
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:23:17-69
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:23:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:25:17-77
	android:name
		ADDED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:25:27-74
uses-sdk
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\172f621333dc3168d5076c7988f84bf2\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\172f621333dc3168d5076c7988f84bf2\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b790fb9b51a9464d836e63572974ac05\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b790fb9b51a9464d836e63572974ac05\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a47391491fc1ec84f4f0ba2baba0288\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a47391491fc1ec84f4f0ba2baba0288\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-runtime-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0655a30f88a218b30140f08f2fcf0f5\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0655a30f88a218b30140f08f2fcf0f5\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6089407a50a854e03941de03facdaf6\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6089407a50a854e03941de03facdaf6\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\832a5e12fc2e9b348024cfc52e216f6d\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\832a5e12fc2e9b348024cfc52e216f6d\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d16eb54be87cc3f72afc33985a122320\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d16eb54be87cc3f72afc33985a122320\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0356b8c03d17076269fa25c954e232f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0356b8c03d17076269fa25c954e232f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e4694d548e0bde5f47d9450211c39c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e4694d548e0bde5f47d9450211c39c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3de91b7448f871adef90601c18d9bac7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3de91b7448f871adef90601c18d9bac7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab961e0b323c8738ead817fe61c7f0b8\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab961e0b323c8738ead817fe61c7f0b8\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\57eedf221066f8277e5ccc62329334e3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\57eedf221066f8277e5ccc62329334e3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\47626705b54ede4907bb1bb15dac0b3d\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\47626705b54ede4907bb1bb15dac0b3d\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e995c29488c705e757d2dbb2f1ba737\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e995c29488c705e757d2dbb2f1ba737\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57cd720ac1eca1884031e0a6e8daf30\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57cd720ac1eca1884031e0a6e8daf30\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d255fb4602b7bd597f4d7067285752cb\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d255fb4602b7bd597f4d7067285752cb\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f907e2130997849173537d35f867bc4c\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f907e2130997849173537d35f867bc4c\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2ffef7aab69652caef300e398bf0698\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2ffef7aab69652caef300e398bf0698\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa05b2c24ef13277a477467c689a79a2\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa05b2c24ef13277a477467c689a79a2\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56123d49d534a0872b4085675af9b2df\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56123d49d534a0872b4085675af9b2df\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be53877d6e443712766bc7362aa0a6a7\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be53877d6e443712766bc7362aa0a6a7\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a4f324cab8aff96a85f3dcdfe887bf0\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a4f324cab8aff96a85f3dcdfe887bf0\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d803fbe453431f76308adefa50f600\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4d803fbe453431f76308adefa50f600\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88de4aa178c89e467e05d5005edfb3fb\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88de4aa178c89e467e05d5005edfb3fb\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c936241dc276c958afd9a34321d2a142\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c936241dc276c958afd9a34321d2a142\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\566720ef7ba90c240bdbe93e9fd63fc8\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\566720ef7ba90c240bdbe93e9fd63fc8\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ed56cb90dabc9f2e2d9d2136613ed92\transformed\lifecycle-livedata-core-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ed56cb90dabc9f2e2d9d2136613ed92\transformed\lifecycle-livedata-core-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\00924bf91eb594b39eb8cdc56310da33\transformed\lifecycle-viewmodel-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\00924bf91eb594b39eb8cdc56310da33\transformed\lifecycle-viewmodel-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8457a356bdfd2f36349fce308320bc1\transformed\lifecycle-livedata-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8457a356bdfd2f36349fce308320bc1\transformed\lifecycle-livedata-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9b737eaf88280bea7e98388990cb76\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9b737eaf88280bea7e98388990cb76\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dffb885357fb91405589e6f55004587c\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dffb885357fb91405589e6f55004587c\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de488430ac7339ea3295e60325bd15d9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de488430ac7339ea3295e60325bd15d9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f8fb1440854fb40ed252f0e17bff4f0\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f8fb1440854fb40ed252f0e17bff4f0\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f590eaf319c20c8360e88a8e9e93197f\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f590eaf319c20c8360e88a8e9e93197f\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc055364240d6a8f1965da7f58bd3942\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc055364240d6a8f1965da7f58bd3942\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2b9b93b87687c4c7d73c7dd525a92e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac2b9b93b87687c4c7d73c7dd525a92e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\17125649c9a2e9297cc7eff77eda4bb1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\17125649c9a2e9297cc7eff77eda4bb1\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\42079e344a0b5d170d617454d1de27ff\transformed\lifecycle-viewmodel-2.9.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\42079e344a0b5d170d617454d1de27ff\transformed\lifecycle-viewmodel-2.9.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\395ca52991126ad48466f9663dd12c85\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\395ca52991126ad48466f9663dd12c85\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\04f856bfa19d0797e812242b358c314e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\04f856bfa19d0797e812242b358c314e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5661e7ae791ba8e41f4a59a0575d81c9\transformed\lifecycle-livedata-core-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5661e7ae791ba8e41f4a59a0575d81c9\transformed\lifecycle-livedata-core-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\38b911cdb4aff11ef71c27b92218e01e\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\38b911cdb4aff11ef71c27b92218e01e\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7f0597caab3d480fa1c8e4718aa77f4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7f0597caab3d480fa1c8e4718aa77f4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9236191ebbd56371186526fe7595f2a2\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9236191ebbd56371186526fe7595f2a2\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\391a5066ef22ceff677de3e645b947ad\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\391a5066ef22ceff677de3e645b947ad\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547c9f48917163359dd028d00faaea5d\transformed\activity-ktx-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547c9f48917163359dd028d00faaea5d\transformed\activity-ktx-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ee85b90936406b9aa2c433fd622ab93\transformed\activity-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ee85b90936406b9aa2c433fd622ab93\transformed\activity-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23dfb108c822dd0b8a4d0a6fa594f3ee\transformed\activity-compose-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23dfb108c822dd0b8a4d0a6fa594f3ee\transformed\activity-compose-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb19184049a54f3cf914e69635db97d7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb19184049a54f3cf914e69635db97d7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad8c275ad12fe7edf1c4e52f6a3a87a2\transformed\core-ktx-1.17.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad8c275ad12fe7edf1c4e52f6a3a87a2\transformed\core-ktx-1.17.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5584ef833df34b2c448068d6a33d2e9c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5584ef833df34b2c448068d6a33d2e9c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\549cf5e4880f4db2dab2a693ba979d58\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\549cf5e4880f4db2dab2a693ba979d58\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12741c8546b01cf589ebbd2ad2926dbe\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12741c8546b01cf589ebbd2ad2926dbe\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eedbb90067f8043472c2f37c4e4f010\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eedbb90067f8043472c2f37c4e4f010\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cae152fce28a8a82bb94ab95a2acb8c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cae152fce28a8a82bb94ab95a2acb8c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5279eacb29d2014c1c50d46d56630da\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5279eacb29d2014c1c50d46d56630da\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f174e659b91022016360435562b6416\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f174e659b91022016360435562b6416\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0b4bf2e207b8393423e4476ab8f1a32\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0b4bf2e207b8393423e4476ab8f1a32\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b00696eb8b4ee2faccdc004cf055301\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b00696eb8b4ee2faccdc004cf055301\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e39619202ef2ac0ac8dcb893726a0142\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e39619202ef2ac0ac8dcb893726a0142\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1301c549d9f105af2e7063d2b5562e4\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1301c549d9f105af2e7063d2b5562e4\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80ca90e0f0e3f96cb6a4b25520c15249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80ca90e0f0e3f96cb6a4b25520c15249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c651c0fcef1e85380bbfecffa5db8690\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c651c0fcef1e85380bbfecffa5db8690\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\144117b64fd39f9a8628bbd6bb48aae9\transformed\ui-tooling\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01b03add0455c623c727c2f2e842d046\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\762da11ad70931e83e3742d367055d1e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04fa016494095076845df2c54bd7f959\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:23:9-81
permission#com.example.sampletest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.sampletest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87273081a2a1619056a564e79ed1c26\transformed\core-1.17.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f76e9270fa7e6933aca00d514560a63\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5794057b6977623e1bf9e6a5fc34264\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee737ef73a30d044000c2f55b03aed22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
