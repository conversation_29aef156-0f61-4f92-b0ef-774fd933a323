package com.example.sampletest.domain.usecase

import com.example.sampletest.domain.repository.WeatherRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GetWeatherUseCase @Inject constructor(
    private val weatherRepository: WeatherRepository
) {
    // Ho Chi Minh City coordinates
    private val HO_CHI_MINH_LAT = 10.8231
    private val HO_CHI_MINH_LON = 106.6297

    suspend fun getHoChiMinhWeather(): Flow<Result<String>> {
        return weatherRepository.getWeatherDescription(HO_CHI_MINH_LAT, HO_CHI_MINH_LON)
    }
}
