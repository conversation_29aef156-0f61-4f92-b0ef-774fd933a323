{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\391a5066ef22ceff677de3e645b947ad\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,287,372,471,572,661,738,831,922,1004,1085,1167,1239,1326,1402,1482,1556,1633,1705", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,86,75,79,73,76,71,121", "endOffsets": "282,367,466,567,656,733,826,917,999,1080,1162,1234,1321,1397,1477,1551,1628,1700,1822"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1020,1105,1204,1305,1394,1471,7825,7916,7998,8079,8161,8233,8320,8396,8476,8651,8728,8800", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,86,75,79,73,76,71,121", "endOffsets": "1015,1100,1199,1300,1389,1466,1559,7911,7993,8074,8156,8228,8315,8391,8471,8545,8723,8795,8917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0356b8c03d17076269fa25c954e232f\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4667,4751,4836,4936,5016,5101,5198,5301,5398,5503,5593,5701,5804,5914,6032,6112,6217", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4662,4746,4831,4931,5011,5096,5193,5296,5393,5498,5588,5696,5799,5909,6027,6107,6212,6311"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1564,1680,1793,1902,2016,2113,2214,2332,2469,2591,2743,2833,2929,3027,3129,3247,3370,3471,3603,3735,3864,4031,4153,4277,4404,4526,4625,4724,4845,4966,5069,5180,5288,5427,5571,5679,5785,5868,5966,6063,6176,6260,6345,6445,6525,6610,6707,6810,6907,7012,7102,7210,7313,7423,7541,7621,7726", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "1675,1788,1897,2011,2108,2209,2327,2464,2586,2738,2828,2924,3022,3124,3242,3365,3466,3598,3730,3859,4026,4148,4272,4399,4521,4620,4719,4840,4961,5064,5175,5283,5422,5566,5674,5780,5863,5961,6058,6171,6255,6340,6440,6520,6605,6702,6805,6902,7007,7097,7205,7308,7418,7536,7616,7721,7820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f907e2130997849173537d35f867bc4c\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,232", "endColumns": "86,89,89", "endOffsets": "137,227,317"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8922,9012", "endColumns": "86,89,89", "endOffsets": "187,9007,9097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f87273081a2a1619056a564e79ed1c26\\transformed\\core-1.17.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "192,288,390,488,593,698,810,8550", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "283,385,483,588,693,805,921,8646"}}]}]}