package com.example.sampletest.presentation.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a \u0010\u0006\u001a\u00020\u00012\b\u0010\u0007\u001a\u0004\u0018\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u00a8\u0006\n"}, d2 = {"AddNoteScreen", "", "viewModel", "Lcom/example/sampletest/presentation/viewmodel/NoteViewModel;", "onSaveDone", "Lkotlin/Function0;", "WeatherSection", "weatherDescription", "", "onAttachWeather", "app_debug"})
public final class AddNoteScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void AddNoteScreen(@org.jetbrains.annotations.NotNull()
    com.example.sampletest.presentation.viewmodel.NoteViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSaveDone) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WeatherSection(java.lang.String weatherDescription, kotlin.jvm.functions.Function0<kotlin.Unit> onAttachWeather) {
    }
}