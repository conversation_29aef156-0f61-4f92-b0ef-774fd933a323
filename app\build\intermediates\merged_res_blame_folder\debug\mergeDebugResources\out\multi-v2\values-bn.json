{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0356b8c03d17076269fa25c954e232f\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1544,1682,1819,1938,2072,2189,2288,2404,2546,2667,2809,2894,3000,3094,3195,3324,3453,3564,3693,3820,3950,4130,4252,4372,4494,4625,4720,4815,4948,5095,5192,5297,5407,5534,5666,5773,5874,5951,6054,6154,6260,6351,6441,6544,6624,6709,6810,6914,7007,7112,7199,7305,7404,7512,7630,7710,7810", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "1677,1814,1933,2067,2184,2283,2399,2541,2662,2804,2889,2995,3089,3190,3319,3448,3559,3688,3815,3945,4125,4247,4367,4489,4620,4715,4810,4943,5090,5187,5292,5402,5529,5661,5768,5869,5946,6049,6149,6255,6346,6436,6539,6619,6704,6805,6909,7002,7107,7194,7300,7399,7507,7625,7705,7805,7899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f907e2130997849173537d35f867bc4c\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8999,9084", "endColumns": "72,84,84", "endOffsets": "173,9079,9164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f87273081a2a1619056a564e79ed1c26\\transformed\\core-1.17.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "178,277,379,481,584,685,787,8637", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "272,374,476,579,680,782,902,8733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\391a5066ef22ceff677de3e645b947ad\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,357,447,545,631,710,816,903,992,1070,1151,1234,1320,1396,1473,1549,1624,1692", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "268,352,442,540,626,705,811,898,987,1065,1146,1229,1315,1391,1468,1544,1619,1687,1805"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "907,1001,1085,1175,1273,1359,1438,7904,7991,8080,8158,8239,8322,8408,8484,8561,8738,8813,8881", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "996,1080,1170,1268,1354,1433,1539,7986,8075,8153,8234,8317,8403,8479,8556,8632,8808,8876,8994"}}]}]}