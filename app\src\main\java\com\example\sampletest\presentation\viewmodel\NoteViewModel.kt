package com.example.sampletest.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sampletest.data.local.NoteDao
import com.example.sampletest.domain.model.Note
import com.example.sampletest.domain.usecase.GetWeatherUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NoteViewModel @Inject constructor(
    private val noteDao: NoteDao,
    private val getWeatherUseCase: GetWeatherUseCase
) : ViewModel() {
    private val _notes = MutableStateFlow<List<Note>>(emptyList())
    val notes: StateFlow<List<Note>> = _notes.asStateFlow()

    private val _weather = MutableStateFlow<String?>(null);
    val weather: StateFlow<String?> = _weather.asStateFlow()

    init {
        loadNotes()
    }

    private fun loadNotes() {
        viewModelScope.launch {
            _notes.value = noteDao.getAll()
        }
    }

    fun addNote(title: String, content: String, weatherDescription: String? = null, onComplete: () -> Unit) {
        if (title.isBlank() || content.isBlank()) return
        viewModelScope.launch {
            noteDao.insert(Note(title = title, content = content, weatherDescription = weatherDescription))
            loadNotes()
            onComplete()
        }
    }

    fun fetchWeather() {
        viewModelScope.launch {
            getWeatherUseCase.getHoChiMinhWeather()
                .collect { result -> {
                    _weather.value = result.getOrNull();
                } }
        }
    }

    fun clearWeatherState() {
        _weather.value = null;
    }
}