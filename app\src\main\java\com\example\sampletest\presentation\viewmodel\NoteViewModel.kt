package com.example.sampletest.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sampletest.data.local.NoteDao
import com.example.sampletest.domain.model.Note
import com.example.sampletest.domain.usecase.GetWeatherUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NoteViewModel @Inject constructor(
    private val noteDao: NoteDao,
    private val getWeatherUseCase: GetWeatherUseCase
) : ViewModel() {
    private val _notes = MutableStateFlow<List<Note>>(emptyList())
    val notes: StateFlow<List<Note>> = _notes.asStateFlow()

    private val _weatherState = MutableStateFlow<WeatherState>(WeatherState.Idle)
    val weatherState: StateFlow<WeatherState> = _weatherState.asStateFlow()

    sealed class WeatherState {
        object Idle : WeatherState()
        object Loading : WeatherState()
        data class Success(val description: String) : WeatherState()
        data class Error(val message: String) : WeatherState()
    }

    init {
        loadNotes()
    }

    private fun loadNotes() {
        viewModelScope.launch {
            _notes.value = noteDao.getAll()
        }
    }

    fun addNote(title: String, content: String, weatherDescription: String? = null, onComplete: () -> Unit) {
        if (title.isBlank() || content.isBlank()) return
        viewModelScope.launch {
            noteDao.insert(Note(title = title, content = content, weatherDescription = weatherDescription))
            loadNotes()
            onComplete()
        }
    }

    fun fetchWeather() {
        viewModelScope.launch {
            _weatherState.value = WeatherState.Loading
            getWeatherUseCase.getHoChiMinhWeather()
                .catch { e ->
                    _weatherState.value = WeatherState.Error(e.message ?: "Unknown error")
                }
                .collect { result ->
                    result.fold(
                        onSuccess = { description ->
                            _weatherState.value = WeatherState.Success(description)
                        },
                        onFailure = { error ->
                            _weatherState.value = WeatherState.Error(error.message ?: "Weather fetch failed")
                        }
                    )
                }
        }
    }

    fun clearWeatherState() {
        _weatherState.value = WeatherState.Idle
    }

    fun updateNote(note: Note) {
        viewModelScope.launch {
            noteDao.update(note)
            loadNotes()
        }
    }

    fun deleteNote(id: Int) {
        viewModelScope.launch {
            noteDao.delete(id)
            loadNotes()
        }
    }
}