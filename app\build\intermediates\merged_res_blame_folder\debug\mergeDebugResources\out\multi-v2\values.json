{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\000d36e943d774f8beab659593bc2726\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6497", "endColumns": "42", "endOffsets": "6535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\de09855bd61fa252d63075166b306596\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "79,89,113,331,336", "startColumns": "4,4,4,4,4", "startOffsets": "4901,5416,6704,19009,19179", "endLines": "79,89,113,335,339", "endColumns": "56,64,63,24,24", "endOffsets": "4953,5476,6763,19174,19323"}}, {"source": "D:\\Driver_D\\RMIT(Android)\\SampleTest\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "47", "endOffsets": "59"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "7047", "endColumns": "47", "endOffsets": "7090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4f530796092af4402355a3f59186f856\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "6600", "endColumns": "53", "endOffsets": "6649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,66", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3818"}, "to": {"startLines": "75,77,78,81,82,114,127,128,129,130,131,132,133,195,196,197,198,199,200,201,202,203,205,206,207,210,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4658,4788,4846,5024,5075,6768,7652,7717,7771,7837,7938,7996,8048,12549,12611,12665,12715,12769,12815,12869,12915,12957,13068,13115,13151,13352,14332", "endLines": "75,77,78,81,82,114,127,128,129,130,131,132,133,195,196,197,198,199,200,201,202,203,205,206,207,212,229", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12", "endOffsets": "4727,4841,4896,5070,5125,6816,7712,7766,7832,7933,7991,8043,8103,12606,12660,12710,12764,12810,12864,12910,12952,12992,13110,13146,13236,13459,14509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2b3fa52ff9069c70a089af782a634aca\\transformed\\activity-1.11.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "88,110", "startColumns": "4,4", "startOffsets": "5374,6540", "endColumns": "41,59", "endOffsets": "5411,6595"}}, {"source": "D:\\Driver_D\\RMIT(Android)\\SampleTest\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "7,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "491,796,843,890,937,982,1027", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "528,838,885,932,977,1022,1064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57edb1afd5dd314dc242ac8a0fca3ba1\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "117", "startColumns": "4", "startOffsets": "6964", "endColumns": "82", "endOffsets": "7042"}}, {"source": "D:\\Driver_D\\RMIT(Android)\\SampleTest\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "87", "endOffsets": "139"}, "to": {"startLines": "241", "startColumns": "4", "startOffsets": "15272", "endColumns": "86", "endOffsets": "15354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fff549d7b1e992263fe93e7e4ad84fd3\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "379,392,398,404,413", "startColumns": "4,4,4,4,4", "startOffsets": "20177,20816,21060,21307,21670", "endLines": "391,397,403,406,417", "endColumns": "24,24,24,24,24", "endOffsets": "20811,21055,21302,21435,21847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,102,103,107,108,109,110,116,126,161,182,190,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,176,236,308,396,461,527,596,659,729,797,869,939,1000,1074,1147,1208,1269,1331,1395,1457,1518,1586,1686,1746,1812,1885,1954,2011,2063,2125,2197,2273,2338,2397,2456,2516,2576,2636,2696,2756,2816,2876,2936,2996,3056,3115,3175,3235,3295,3355,3415,3475,3535,3595,3655,3715,3774,3834,3894,3953,4012,4071,4130,4189,4248,4283,4318,4373,4436,4491,4549,4605,4663,4724,4787,4844,4895,4953,5003,5064,5121,5187,5221,5256,5291,5361,5428,5500,5569,5638,5712,5784,5872,5943,6060,6261,6371,6572,6701,6773,6840,7043,7344,9134,9799,10047,10729", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,101,102,106,107,108,109,115,125,160,181,189,222,228", "endColumns": "63,56,59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24,24", "endOffsets": "114,171,231,303,391,456,522,591,654,724,792,864,934,995,1069,1142,1203,1264,1326,1390,1452,1513,1581,1681,1741,1807,1880,1949,2006,2058,2120,2192,2268,2333,2392,2451,2511,2571,2631,2691,2751,2811,2871,2931,2991,3051,3110,3170,3230,3290,3350,3410,3470,3530,3590,3650,3710,3769,3829,3889,3948,4007,4066,4125,4184,4243,4278,4313,4368,4431,4486,4544,4600,4658,4719,4782,4839,4890,4948,4998,5059,5116,5182,5216,5251,5286,5356,5423,5495,5564,5633,5707,5779,5867,5938,6055,6256,6366,6567,6696,6768,6835,7038,7339,9129,9794,10042,10724,10891"}, "to": {"startLines": "2,3,4,5,6,8,9,10,11,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,84,85,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,116,120,121,122,123,124,125,126,204,230,231,235,236,240,242,243,251,257,267,302,323,340,373", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,214,271,331,403,533,598,664,733,1069,1139,1207,1279,1349,1410,1484,1557,1618,1679,1741,1805,1867,1928,1996,2096,2156,2222,2295,2364,2421,2473,2535,2607,2683,2748,2807,2866,2926,2986,3046,3106,3166,3226,3286,3346,3406,3466,3525,3585,3645,3705,3765,3825,3885,3945,4005,4065,4125,4184,4244,4304,4363,4422,4481,4540,4599,5184,5219,5481,5536,5599,5654,5712,5768,5826,5887,5950,6007,6058,6116,6166,6227,6284,6350,6384,6419,6894,7141,7208,7280,7349,7418,7492,7564,12997,14514,14631,14832,14942,15143,15359,15431,15802,16005,16306,18096,18761,19328,20010", "endLines": "2,3,4,5,6,8,9,10,11,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,84,85,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,116,120,121,122,123,124,125,126,204,230,234,235,239,240,242,243,256,266,301,322,330,372,378", "endColumns": "63,56,59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24,24", "endOffsets": "209,266,326,398,486,593,659,728,791,1134,1202,1274,1344,1405,1479,1552,1613,1674,1736,1800,1862,1923,1991,2091,2151,2217,2290,2359,2416,2468,2530,2602,2678,2743,2802,2861,2921,2981,3041,3101,3161,3221,3281,3341,3401,3461,3520,3580,3640,3700,3760,3820,3880,3940,4000,4060,4120,4179,4239,4299,4358,4417,4476,4535,4594,4653,5214,5249,5531,5594,5649,5707,5763,5821,5882,5945,6002,6053,6111,6161,6222,6279,6345,6379,6414,6449,6959,7203,7275,7344,7413,7487,7559,7647,13063,14626,14827,14937,15138,15267,15426,15493,16000,16301,18091,18756,19004,20005,20172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1722c3a48fb7870bcbbd5469fcf71460\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "4958", "endColumns": "65", "endOffsets": "5019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,111,157,213", "endColumns": "55,45,55,54", "endOffsets": "106,152,208,263"}, "to": {"startLines": "76,119,208,209", "startColumns": "4,4,4,4", "startOffsets": "4732,7095,13241,13297", "endColumns": "55,45,55,54", "endOffsets": "4783,7136,13292,13347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4c418d75ea5be54311f1c76fed18e630\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "6654", "endColumns": "49", "endOffsets": "6699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6760d9b14ae2136c5d362bb380ff18c3\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "83,87", "startColumns": "4,4", "startOffsets": "5130,5307", "endColumns": "53,66", "endOffsets": "5179,5369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ac8e8bbf722257eb68be5834964871a\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "86,244,407,410", "startColumns": "4,4,4,4", "startOffsets": "5254,15498,21440,21555", "endLines": "86,250,409,412", "endColumns": "52,24,24,24", "endOffsets": "5302,15797,21550,21665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13de6f7c29989995feb8c88e5c0abe2a\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6454", "endColumns": "42", "endOffsets": "6492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "115,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,213,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6821,8108,8196,8282,8363,8447,8516,8581,8664,8770,8856,8976,9030,9099,9160,9229,9318,9413,9487,9584,9677,9775,9924,10015,10103,10199,10297,10361,10429,10516,10610,10677,10749,10821,10922,11031,11107,11176,11224,11290,11354,11428,11485,11542,11614,11664,11718,11789,11860,11930,11999,12057,12133,12204,12278,12364,12414,12484,13464,14179", "endLines": "115,134,135,136,137,138,139,140,141,142,145,146,147,148,149,150,151,152,153,154,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,222,225", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "6889,8191,8277,8358,8442,8511,8576,8659,8765,8851,8971,9025,9094,9155,9224,9313,9408,9482,9579,9672,9770,9919,10010,10098,10194,10292,10356,10424,10511,10605,10672,10744,10816,10917,11026,11102,11171,11219,11285,11349,11423,11480,11537,11609,11659,11713,11784,11855,11925,11994,12052,12128,12199,12273,12359,12409,12479,12544,14174,14327"}}]}]}