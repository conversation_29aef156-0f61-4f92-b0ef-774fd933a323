package com.example.sampletest.data.repository

import com.example.sampletest.BuildConfig
import com.example.sampletest.data.remote.network.WeatherApi
import com.example.sampletest.domain.repository.WeatherRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WeatherRepositoryImpl @Inject constructor(
    private val weatherApi: WeatherApi
) : WeatherRepository {

    override suspend fun getWeatherDescription(latitude: Double, longitude: Double): Flow<Result<String>> = flow {
        try {
            val response = weatherApi.getWeatherInfo(
                latitude = latitude,
                longitude = longitude,
                apiKey = BuildConfig.WEATHER_API_KEY,
                units = "metric"
            )
            
            if (response.isSuccessful) {
                val weatherResponse = response.body()
                if (weatherResponse != null) {
                    // Format weather description: "Sunny, 30°C"
                    val condition = weatherResponse.weather.firstOrNull()?.main ?: "Unknown"
                    val temperature = weatherResponse.main.temperature.toInt()
                    val description = "$condition, ${temperature}°C"
                    emit(Result.success(description))
                } else {
                    emit(Result.failure(Exception("Empty weather response")))
                }
            } else {
                emit(Result.failure(Exception("Weather API error: ${response.code()}")))
            }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }
}
