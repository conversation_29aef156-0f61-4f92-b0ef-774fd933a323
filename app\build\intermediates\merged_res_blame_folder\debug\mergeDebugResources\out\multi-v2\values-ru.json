{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,233", "endColumns": "89,87,90", "endOffsets": "140,228,319"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8930,9018", "endColumns": "89,87,90", "endOffsets": "190,9013,9104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,289,372,470,572,664,746,836,924,1006,1090,1177,1249,1338,1414,1492,1568,1652,1722", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,88,75,77,75,83,69,122", "endOffsets": "284,367,465,567,659,741,831,919,1001,1085,1172,1244,1333,1409,1487,1563,1647,1717,1840"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1015,1098,1196,1298,1390,1472,7820,7908,7990,8074,8161,8233,8322,8398,8476,8653,8737,8807", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,88,75,77,75,83,69,122", "endOffsets": "1010,1093,1191,1293,1385,1467,1557,7903,7985,8069,8156,8228,8317,8393,8471,8547,8732,8802,8925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4650,4744,4831,4939,5019,5103,5201,5302,5396,5491,5579,5686,5784,5883,6030,6110,6216", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4645,4739,4826,4934,5014,5098,5196,5297,5391,5486,5574,5681,5779,5878,6025,6105,6211,6308"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1562,1680,1796,1914,2032,2131,2228,2342,2483,2600,2740,2824,2922,3015,3113,3228,3351,3454,3583,3711,3837,4017,4141,4264,4391,4511,4605,4705,4826,4959,5057,5171,5278,5410,5548,5658,5758,5843,5938,6034,6157,6251,6338,6446,6526,6610,6708,6809,6903,6998,7086,7193,7291,7390,7537,7617,7723", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "1675,1791,1909,2027,2126,2223,2337,2478,2595,2735,2819,2917,3010,3108,3223,3346,3449,3578,3706,3832,4012,4136,4259,4386,4506,4600,4700,4821,4954,5052,5166,5273,5405,5543,5653,5753,5838,5933,6029,6152,6246,6333,6441,6521,6605,6703,6804,6898,6993,7081,7188,7286,7385,7532,7612,7718,7815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "195,293,395,496,597,702,805,8552", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "288,390,491,592,697,800,917,8648"}}]}]}