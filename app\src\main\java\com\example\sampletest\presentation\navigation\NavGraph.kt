package com.example.sampletest.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.sampletest.presentation.ui.AddNoteScreen
import com.example.sampletest.presentation.ui.DetailScreen
import com.example.sampletest.presentation.ui.HomeScreen
import com.example.sampletest.presentation.viewmodel.NoteViewModel

@Composable
fun NavGraph (
    navController: NavHostController
) {
    val sharedViewModel: NoteViewModel = hiltViewModel()

    NavHost(
        navController = navController,
        startDestination = "home"
    ) {
        composable(route = "home") {
            HomeScreen(
                viewModel = sharedViewModel,
                onAddTaskClick = {
                    navController.navigate("add")
                },
                onDetailClick = {
                    navController.navigate("detail")
                }
            )
        }

        composable(route = "add") {
            AddNoteScreen(
                viewModel = sharedViewModel,
                onSaveDone = {
                    navController.popBackStack()
                }
            )
        }

        composable(route = "detail") {
            DetailScreen (
                viewModel = sharedViewModel,
            )
        }
    }
}