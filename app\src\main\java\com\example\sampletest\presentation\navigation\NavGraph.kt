package com.example.sampletest.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.NavType
import com.example.sampletest.presentation.ui.AddNoteScreen
import com.example.sampletest.presentation.ui.DetailScreen
import com.example.sampletest.presentation.ui.HomeScreen
import com.example.sampletest.presentation.viewmodel.NoteViewModel

@Composable
fun NavGraph (
    navController: NavHostController
) {
    val sharedViewModel: NoteViewModel = hiltViewModel()

    NavHost(
        navController = navController,
        startDestination = "home"
    ) {
        composable(route = "home") {
            HomeScreen(
                viewModel = sharedViewModel,
                onAddTaskClick = {
                    navController.navigate("add")
                },
                onDetailClick = { noteId ->
                    navController.navigate("detail/$noteId")
                }
            )
        }

        composable(route = "add") {
            AddNoteScreen(
                viewModel = sharedViewModel,
                onSaveDone = {
                    navController.popBackStack()
                }
            )
        }

        composable(
            route = "detail/{noteId}",
            arguments = listOf(navArgument("noteId") { type = NavType.IntType })
        ) { backStackEntry ->
            val noteId = backStackEntry.arguments?.getInt("noteId") ?: 0
            val notes by sharedViewModel.notes.collectAsState()
            val selectedNote = notes.find { it.id == noteId }

            selectedNote?.let { note ->
                DetailScreen(
                    note = note,
                )
            }
        }
    }
}