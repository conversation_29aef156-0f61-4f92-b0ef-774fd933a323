{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,223", "endColumns": "80,86,102", "endOffsets": "131,218,321"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9041,9128", "endColumns": "80,86,102", "endOffsets": "181,9123,9226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4744,4830,4917,5017,5104,5191,5291,5397,5493,5591,5680,5788,5884,5984,6130,6220,6338", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4739,4825,4912,5012,5099,5186,5286,5392,5488,5586,5675,5783,5879,5979,6125,6215,6333,6429"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1546,1663,1778,1897,2014,2112,2209,2323,2446,2561,2706,2790,2901,2994,3091,3205,3328,3444,3591,3737,3875,4052,4184,4309,4438,4560,4654,4752,4878,5011,5110,5221,5330,5480,5633,5741,5841,5926,6021,6117,6235,6321,6408,6508,6595,6682,6782,6888,6984,7082,7171,7279,7375,7475,7621,7711,7829", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "1658,1773,1892,2009,2107,2204,2318,2441,2556,2701,2785,2896,2989,3086,3200,3323,3439,3586,3732,3870,4047,4179,4304,4433,4555,4649,4747,4873,5006,5105,5216,5325,5475,5628,5736,5836,5921,6016,6112,6230,6316,6403,6503,6590,6677,6777,6883,6979,7077,7166,7274,7370,7470,7616,7706,7824,7920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "186,284,386,486,587,693,796,8662", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "279,381,481,582,688,791,912,8758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,458,561,647,727,816,904,986,1069,1156,1228,1315,1399,1477,1553,1638,1708", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "275,359,453,556,642,722,811,899,981,1064,1151,1223,1310,1394,1472,1548,1633,1703,1826"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1010,1094,1188,1291,1377,1457,7925,8013,8095,8178,8265,8337,8424,8508,8586,8763,8848,8918", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "1005,1089,1183,1286,1372,1452,1541,8008,8090,8173,8260,8332,8419,8503,8581,8657,8843,8913,9036"}}]}]}