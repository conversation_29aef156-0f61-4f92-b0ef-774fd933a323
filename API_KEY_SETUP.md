# Weather API Setup Instructions

## Getting OpenWeatherMap API Key

1. Go to [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for a free account
3. Go to your API keys section
4. Copy your API key

## Adding API Key to Project

1. Open the `local.properties` file in your project root (same level as `build.gradle.kts`)
2. Add this line to the file:
   ```
   WEATHER_API_KEY=your_api_key_here
   ```
   Replace `your_api_key_here` with your actual API key from OpenWeatherMap

3. Save the file

## Important Notes

- The `local.properties` file is already in `.gitignore`, so your API key won't be committed to version control
- The app will show an error if no API key is provided
- Free OpenWeatherMap accounts have a limit of 1000 API calls per day
- The weather data is fetched for Ho Chi Minh City, Vietnam (hardcoded coordinates)

## Testing

After adding your API key:
1. Build and run the app
2. Go to "Add Note" screen
3. Click "Attach Weather Info" button
4. You should see the current weather for Ho Chi Minh City

If you see an error, check:
- API key is correctly added to `local.properties`
- Internet connection is available
- API key is valid and active
