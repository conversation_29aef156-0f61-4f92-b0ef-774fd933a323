{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,86", "endOffsets": "123,208,295"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8947,9032", "endColumns": "72,84,86", "endOffsets": "173,9027,9114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,356,446,543,631,712,805,893,979,1062,1147,1222,1305,1383,1457,1530,1605,1671", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "268,351,441,538,626,707,800,888,974,1057,1142,1217,1300,1378,1452,1525,1600,1666,1783"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "899,993,1076,1166,1263,1351,1432,7863,7951,8037,8120,8205,8280,8363,8441,8515,8689,8764,8830", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "988,1071,1161,1258,1346,1427,1520,7946,8032,8115,8200,8275,8358,8436,8510,8583,8759,8825,8942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4733,4823,4914,5029,5109,5194,5297,5403,5500,5603,5688,5794,5893,5996,6117,6197,6299", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4728,4818,4909,5024,5104,5189,5292,5398,5495,5598,5683,5789,5888,5991,6112,6192,6294,6388"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1525,1643,1765,1881,1999,2097,2194,2309,2444,2568,2708,2793,2897,2993,3093,3210,3340,3449,3593,3736,3865,4063,4188,4307,4430,4568,4665,4760,4884,5008,5109,5214,5320,5463,5612,5718,5822,5898,5994,6091,6203,6293,6384,6499,6579,6664,6767,6873,6970,7073,7158,7264,7363,7466,7587,7667,7769", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "1638,1760,1876,1994,2092,2189,2304,2439,2563,2703,2788,2892,2988,3088,3205,3335,3444,3588,3731,3860,4058,4183,4302,4425,4563,4660,4755,4879,5003,5104,5209,5315,5458,5607,5713,5817,5893,5989,6086,6198,6288,6379,6494,6574,6659,6762,6868,6965,7068,7153,7259,7358,7461,7582,7662,7764,7858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "178,276,378,480,584,687,785,8588", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "271,373,475,579,682,780,894,8684"}}]}]}