/ Header Record For PersistentHashMapValueStorage9 8app/src/main/java/com/example/sampletest/MainActivity.kt2 1app/src/main/java/com/example/sampletest/MyApp.ktC Bapp/src/main/java/com/example/sampletest/data/local/AppDatabase.kt? >app/src/main/java/com/example/sampletest/data/local/NoteDao.ktK Japp/src/main/java/com/example/sampletest/data/remote/network/WeatherApi.ktR Qapp/src/main/java/com/example/sampletest/data/repository/WeatherRepositoryImpl.kt> =app/src/main/java/com/example/sampletest/di/DatabaseModule.kt= <app/src/main/java/com/example/sampletest/di/NetworkModule.kt> =app/src/main/java/com/example/sampletest/domain/model/Note.ktA @app/src/main/java/com/example/sampletest/domain/model/Weather.ktP Oapp/src/main/java/com/example/sampletest/domain/repository/WeatherRepository.ktM Lapp/src/main/java/com/example/sampletest/domain/usecase/GetWeatherUseCase.ktM Lapp/src/main/java/com/example/sampletest/presentation/navigation/NavGraph.ktJ Iapp/src/main/java/com/example/sampletest/presentation/ui/AddNoteScreen.ktI Happ/src/main/java/com/example/sampletest/presentation/ui/DetailScreen.ktG Fapp/src/main/java/com/example/sampletest/presentation/ui/HomeScreen.ktQ Papp/src/main/java/com/example/sampletest/presentation/viewmodel/NoteViewModel.kt; :app/src/main/java/com/example/sampletest/ui/theme/Color.kt; :app/src/main/java/com/example/sampletest/ui/theme/Theme.kt: 9app/src/main/java/com/example/sampletest/ui/theme/Type.kt? >app/src/main/java/com/example/sampletest/data/local/NoteDao.ktR Qapp/src/main/java/com/example/sampletest/data/repository/WeatherRepositoryImpl.ktA @app/src/main/java/com/example/sampletest/domain/model/Weather.ktP Oapp/src/main/java/com/example/sampletest/domain/repository/WeatherRepository.ktM Lapp/src/main/java/com/example/sampletest/domain/usecase/GetWeatherUseCase.ktQ Papp/src/main/java/com/example/sampletest/presentation/viewmodel/NoteViewModel.kt