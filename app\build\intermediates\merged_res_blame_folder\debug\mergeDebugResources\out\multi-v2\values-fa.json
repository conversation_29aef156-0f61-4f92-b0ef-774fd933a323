{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f87273081a2a1619056a564e79ed1c26\\transformed\\core-1.17.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "184,283,385,484,584,685,791,8394", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "278,380,479,579,680,786,903,8490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f907e2130997849173537d35f867bc4c\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,223", "endColumns": "78,88,88", "endOffsets": "129,218,307"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8754,8843", "endColumns": "78,88,88", "endOffsets": "179,8838,8927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\391a5066ef22ceff677de3e645b947ad\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,272,351,445,543,629,711,802,887,970,1051,1133,1207,1291,1366,1440,1512,1587,1654", "endColumns": "86,78,93,97,85,81,90,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "267,346,440,538,624,706,797,882,965,1046,1128,1202,1286,1361,1435,1507,1582,1649,1766"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "908,995,1074,1168,1266,1352,1434,7684,7769,7852,7933,8015,8089,8173,8248,8322,8495,8570,8637", "endColumns": "86,78,93,97,85,81,90,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "990,1069,1163,1261,1347,1429,1520,7764,7847,7928,8010,8084,8168,8243,8317,8389,8565,8632,8749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0356b8c03d17076269fa25c954e232f\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1525,1640,1750,1864,1982,2078,2174,2288,2427,2543,2678,2763,2866,2958,3055,3169,3292,3400,3533,3664,3786,3951,4073,4186,4302,4419,4512,4610,4731,4863,4970,5073,5178,5309,5445,5551,5661,5741,5834,5931,6052,6138,6222,6321,6403,6487,6588,6689,6786,6886,6973,7077,7177,7280,7400,7482,7586", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "1635,1745,1859,1977,2073,2169,2283,2422,2538,2673,2758,2861,2953,3050,3164,3287,3395,3528,3659,3781,3946,4068,4181,4297,4414,4507,4605,4726,4858,4965,5068,5173,5304,5440,5546,5656,5736,5829,5926,6047,6133,6217,6316,6398,6482,6583,6684,6781,6881,6968,7072,7172,7275,7395,7477,7581,7679"}}]}]}