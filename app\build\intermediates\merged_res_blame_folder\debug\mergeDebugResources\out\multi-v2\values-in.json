{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1544,1663,1778,1890,2006,2104,2210,2333,2480,2603,2753,2840,2944,3037,3141,3259,3379,3488,3628,3766,3895,4073,4195,4315,4438,4561,4655,4756,4876,5009,5111,5218,5325,5467,5614,5723,5823,5899,5995,6090,6208,6297,6382,6481,6561,6644,6743,6842,6939,7039,7126,7229,7328,7432,7549,7629,7734", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "1658,1773,1885,2001,2099,2205,2328,2475,2598,2748,2835,2939,3032,3136,3254,3374,3483,3623,3761,3890,4068,4190,4310,4433,4556,4650,4751,4871,5004,5106,5213,5320,5462,5609,5718,5818,5894,5990,6085,6203,6292,6377,6476,6556,6639,6738,6837,6934,7034,7121,7224,7323,7427,7544,7624,7729,7824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,275,357,455,555,641,724,815,902,987,1069,1152,1224,1316,1393,1470,1543,1621,1687", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "270,352,450,550,636,719,810,897,982,1064,1147,1219,1311,1388,1465,1538,1616,1682,1801"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1004,1086,1184,1284,1370,1453,7829,7916,8001,8083,8166,8238,8330,8407,8484,8658,8736,8802", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "999,1081,1179,1279,1365,1448,1539,7911,7996,8078,8161,8233,8325,8402,8479,8552,8731,8797,8916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "184,279,381,478,575,681,799,8557", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "274,376,473,570,676,794,909,8653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,220", "endColumns": "78,85,89", "endOffsets": "129,215,305"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8921,9007", "endColumns": "78,85,89", "endOffsets": "179,9002,9092"}}]}]}