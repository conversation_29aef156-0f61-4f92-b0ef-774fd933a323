package com.example.tutorialagain.data.remote

object ApiClient {
    val studentApi: StudentApi by lazy {
        Retrofit.Builder()
            .baseUrl("https://my-json-\n" +
                    "server.typicode.com/minhthanhvu/lecture04/students")
            .addConverterFactory(Json.asConverterFactory("application/json".toMediaType()))
            .build()
            .create(StudentApi::class.java)
    }
}