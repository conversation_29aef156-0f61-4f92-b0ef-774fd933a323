{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0356b8c03d17076269fa25c954e232f\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4048,4131,4213,4302,4382,4464,4561,4655,4748,4841,4925,5022,5118,5213,5321,5401,5495", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4043,4126,4208,4297,4377,4459,4556,4650,4743,4836,4920,5017,5113,5208,5316,5396,5490,5582"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1397,1501,1604,1708,1810,1902,1990,2094,2199,2304,2420,2502,2598,2682,2770,2875,2988,3089,3198,3305,3413,3530,3635,3736,3840,3945,4030,4125,4230,4339,4429,4529,4627,4738,4854,4954,5045,5119,5209,5298,5390,5473,5555,5644,5724,5806,5903,5997,6090,6183,6267,6364,6460,6555,6663,6743,6837", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "1496,1599,1703,1805,1897,1985,2089,2194,2299,2415,2497,2593,2677,2765,2870,2983,3084,3193,3300,3408,3525,3630,3731,3835,3940,4025,4120,4225,4334,4424,4524,4622,4733,4849,4949,5040,5114,5204,5293,5385,5468,5550,5639,5719,5801,5898,5992,6085,6178,6262,6359,6455,6550,6658,6738,6832,6924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f907e2130997849173537d35f867bc4c\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,209", "endColumns": "70,82,78", "endOffsets": "121,204,283"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,7954,8037", "endColumns": "70,82,78", "endOffsets": "171,8032,8111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f87273081a2a1619056a564e79ed1c26\\transformed\\core-1.17.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "176,268,367,461,555,648,741,7595", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "263,362,456,550,643,736,832,7691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\391a5066ef22ceff677de3e645b947ad\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,417,508,586,660,737,815,890,963,1038,1106,1187,1260,1332,1403,1477,1545", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "249,325,412,503,581,655,732,810,885,958,1033,1101,1182,1255,1327,1398,1472,1540,1656"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,914,990,1077,1168,1246,1320,6929,7007,7082,7155,7230,7298,7379,7452,7524,7696,7770,7838", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "909,985,1072,1163,1241,1315,1392,7002,7077,7150,7225,7293,7374,7447,7519,7590,7765,7833,7949"}}]}]}