package com.example.sampletest.domain.usecase;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tH\u0086@\u00a2\u0006\u0002\u0010\fR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/sampletest/domain/usecase/GetWeatherUseCase;", "", "weatherRepository", "Lcom/example/sampletest/domain/repository/WeatherRepository;", "(Lcom/example/sampletest/domain/repository/WeatherRepository;)V", "HO_CHI_MINH_LAT", "", "HO_CHI_MINH_LON", "getHoChiMinhWeather", "Lkotlinx/coroutines/flow/Flow;", "Lkotlin/Result;", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class GetWeatherUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sampletest.domain.repository.WeatherRepository weatherRepository = null;
    private final double HO_CHI_MINH_LAT = 10.8231;
    private final double HO_CHI_MINH_LON = 106.6297;
    
    @javax.inject.Inject()
    public GetWeatherUseCase(@org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.repository.WeatherRepository weatherRepository) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getHoChiMinhWeather(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<kotlin.Result<java.lang.String>>> $completion) {
        return null;
    }
}