{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,409,527,629,724,836,974,1090,1236,1320,1420,1512,1611,1729,1853,1958,2095,2229,2373,2562,2700,2823,2947,3073,3166,3262,3387,3528,3623,3734,3843,3982,4127,4238,4337,4414,4508,4602,4722,4810,4893,4998,5084,5167,5266,5367,5462,5560,5648,5754,5854,5957,6085,6170,6284", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "169,285,404,522,624,719,831,969,1085,1231,1315,1415,1507,1606,1724,1848,1953,2090,2224,2368,2557,2695,2818,2942,3068,3161,3257,3382,3523,3618,3729,3838,3977,4122,4233,4332,4409,4503,4597,4717,4805,4888,4993,5079,5162,5261,5362,5457,5555,5643,5749,5849,5952,6080,6165,6279,6386"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1577,1696,1812,1931,2049,2151,2246,2358,2496,2612,2758,2842,2942,3034,3133,3251,3375,3480,3617,3751,3895,4084,4222,4345,4469,4595,4688,4784,4909,5050,5145,5256,5365,5504,5649,5760,5859,5936,6030,6124,6244,6332,6415,6520,6606,6689,6788,6889,6984,7082,7170,7276,7376,7479,7607,7692,7806", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "1691,1807,1926,2044,2146,2241,2353,2491,2607,2753,2837,2937,3029,3128,3246,3370,3475,3612,3746,3890,4079,4217,4340,4464,4590,4683,4779,4904,5045,5140,5251,5360,5499,5644,5755,5854,5931,6025,6119,6239,6327,6410,6515,6601,6684,6783,6884,6979,7077,7165,7271,7371,7474,7602,7687,7801,7908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "191,290,392,490,587,695,806,8659", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "285,387,485,582,690,801,923,8755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,287,370,469,571,667,748,841,933,1023,1110,1201,1274,1363,1438,1514,1587,1664,1730", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "282,365,464,566,662,743,836,928,1018,1105,1196,1269,1358,1433,1509,1582,1659,1725,1846"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "928,1023,1106,1205,1307,1403,1484,7913,8005,8095,8182,8273,8346,8435,8510,8586,8760,8837,8903", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "1018,1101,1200,1302,1398,1479,1572,8000,8090,8177,8268,8341,8430,8505,8581,8654,8832,8898,9019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,98", "endOffsets": "136,234,333"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9024,9122", "endColumns": "85,97,98", "endOffsets": "186,9117,9216"}}]}]}