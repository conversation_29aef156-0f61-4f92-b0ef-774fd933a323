  Activity android.app  Application android.app  Bundle android.app.Activity  Context android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Icons androidx.compose.material.icons  Add &androidx.compose.material.icons.filled  Button androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
MaterialTheme androidx.compose.material3  Scaffold androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  Text androidx.compose.material3  	TextField androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  GetWeatherUseCase androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Note androidx.lifecycle.ViewModel  NoteDao androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  NavHostController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  NoteDao androidx.room.RoomDatabase  MainActivity com.example.sampletest  MyApp com.example.sampletest  Bundle #com.example.sampletest.MainActivity  AppDatabase !com.example.sampletest.data.local  Int !com.example.sampletest.data.local  List !com.example.sampletest.data.local  Note !com.example.sampletest.data.local  NoteDao !com.example.sampletest.data.local  OnConflictStrategy !com.example.sampletest.data.local  NoteDao -com.example.sampletest.data.local.AppDatabase  Insert )com.example.sampletest.data.local.NoteDao  Int )com.example.sampletest.data.local.NoteDao  List )com.example.sampletest.data.local.NoteDao  Note )com.example.sampletest.data.local.NoteDao  OnConflictStrategy )com.example.sampletest.data.local.NoteDao  Query )com.example.sampletest.data.local.NoteDao  Update )com.example.sampletest.data.local.NoteDao  Double *com.example.sampletest.data.remote.network  String *com.example.sampletest.data.remote.network  
WeatherApi *com.example.sampletest.data.remote.network  Double 5com.example.sampletest.data.remote.network.WeatherApi  GET 5com.example.sampletest.data.remote.network.WeatherApi  Query 5com.example.sampletest.data.remote.network.WeatherApi  Response 5com.example.sampletest.data.remote.network.WeatherApi  String 5com.example.sampletest.data.remote.network.WeatherApi  WeatherResponse 5com.example.sampletest.data.remote.network.WeatherApi  Double &com.example.sampletest.data.repository  Result &com.example.sampletest.data.repository  String &com.example.sampletest.data.repository  WeatherRepositoryImpl &com.example.sampletest.data.repository  Double <com.example.sampletest.data.repository.WeatherRepositoryImpl  Flow <com.example.sampletest.data.repository.WeatherRepositoryImpl  Inject <com.example.sampletest.data.repository.WeatherRepositoryImpl  Result <com.example.sampletest.data.repository.WeatherRepositoryImpl  String <com.example.sampletest.data.repository.WeatherRepositoryImpl  
WeatherApi <com.example.sampletest.data.repository.WeatherRepositoryImpl  DatabaseModule com.example.sampletest.di  
NetworkModule com.example.sampletest.di  SingletonComponent com.example.sampletest.di  AppDatabase (com.example.sampletest.di.DatabaseModule  ApplicationContext (com.example.sampletest.di.DatabaseModule  Context (com.example.sampletest.di.DatabaseModule  NoteDao (com.example.sampletest.di.DatabaseModule  Provides (com.example.sampletest.di.DatabaseModule  	Singleton (com.example.sampletest.di.DatabaseModule  Provides 'com.example.sampletest.di.NetworkModule  Retrofit 'com.example.sampletest.di.NetworkModule  	Singleton 'com.example.sampletest.di.NetworkModule  
WeatherApi 'com.example.sampletest.di.NetworkModule  WeatherRepository 'com.example.sampletest.di.NetworkModule  Clouds #com.example.sampletest.domain.model  Coordinates #com.example.sampletest.domain.model  Double #com.example.sampletest.domain.model  Int #com.example.sampletest.domain.model  List #com.example.sampletest.domain.model  Long #com.example.sampletest.domain.model  MainWeatherData #com.example.sampletest.domain.model  Note #com.example.sampletest.domain.model  Rain #com.example.sampletest.domain.model  Snow #com.example.sampletest.domain.model  String #com.example.sampletest.domain.model  Sys #com.example.sampletest.domain.model  Weather #com.example.sampletest.domain.model  WeatherResponse #com.example.sampletest.domain.model  Wind #com.example.sampletest.domain.model  Int *com.example.sampletest.domain.model.Clouds  SerializedName *com.example.sampletest.domain.model.Clouds  Double /com.example.sampletest.domain.model.Coordinates  SerializedName /com.example.sampletest.domain.model.Coordinates  Double 3com.example.sampletest.domain.model.MainWeatherData  Int 3com.example.sampletest.domain.model.MainWeatherData  SerializedName 3com.example.sampletest.domain.model.MainWeatherData  Int (com.example.sampletest.domain.model.Note  
PrimaryKey (com.example.sampletest.domain.model.Note  String (com.example.sampletest.domain.model.Note  Double (com.example.sampletest.domain.model.Rain  SerializedName (com.example.sampletest.domain.model.Rain  Double (com.example.sampletest.domain.model.Snow  SerializedName (com.example.sampletest.domain.model.Snow  Int 'com.example.sampletest.domain.model.Sys  Long 'com.example.sampletest.domain.model.Sys  SerializedName 'com.example.sampletest.domain.model.Sys  String 'com.example.sampletest.domain.model.Sys  Int +com.example.sampletest.domain.model.Weather  SerializedName +com.example.sampletest.domain.model.Weather  String +com.example.sampletest.domain.model.Weather  Clouds 3com.example.sampletest.domain.model.WeatherResponse  Coordinates 3com.example.sampletest.domain.model.WeatherResponse  Int 3com.example.sampletest.domain.model.WeatherResponse  List 3com.example.sampletest.domain.model.WeatherResponse  Long 3com.example.sampletest.domain.model.WeatherResponse  MainWeatherData 3com.example.sampletest.domain.model.WeatherResponse  Rain 3com.example.sampletest.domain.model.WeatherResponse  SerializedName 3com.example.sampletest.domain.model.WeatherResponse  Snow 3com.example.sampletest.domain.model.WeatherResponse  String 3com.example.sampletest.domain.model.WeatherResponse  Sys 3com.example.sampletest.domain.model.WeatherResponse  Weather 3com.example.sampletest.domain.model.WeatherResponse  Wind 3com.example.sampletest.domain.model.WeatherResponse  Double (com.example.sampletest.domain.model.Wind  Int (com.example.sampletest.domain.model.Wind  SerializedName (com.example.sampletest.domain.model.Wind  Double (com.example.sampletest.domain.repository  Result (com.example.sampletest.domain.repository  String (com.example.sampletest.domain.repository  WeatherRepository (com.example.sampletest.domain.repository  Double :com.example.sampletest.domain.repository.WeatherRepository  Flow :com.example.sampletest.domain.repository.WeatherRepository  Result :com.example.sampletest.domain.repository.WeatherRepository  String :com.example.sampletest.domain.repository.WeatherRepository  GetWeatherUseCase %com.example.sampletest.domain.usecase  Result %com.example.sampletest.domain.usecase  String %com.example.sampletest.domain.usecase  Flow 7com.example.sampletest.domain.usecase.GetWeatherUseCase  Inject 7com.example.sampletest.domain.usecase.GetWeatherUseCase  Result 7com.example.sampletest.domain.usecase.GetWeatherUseCase  String 7com.example.sampletest.domain.usecase.GetWeatherUseCase  WeatherRepository 7com.example.sampletest.domain.usecase.GetWeatherUseCase  NavGraph .com.example.sampletest.presentation.navigation  
AddNoteScreen &com.example.sampletest.presentation.ui  DetailScreen &com.example.sampletest.presentation.ui  ExperimentalMaterial3Api &com.example.sampletest.presentation.ui  
HomeScreen &com.example.sampletest.presentation.ui  OptIn &com.example.sampletest.presentation.ui  String &com.example.sampletest.presentation.ui  Unit &com.example.sampletest.presentation.ui  WeatherSection &com.example.sampletest.presentation.ui  List -com.example.sampletest.presentation.viewmodel  MutableStateFlow -com.example.sampletest.presentation.viewmodel  
NoteViewModel -com.example.sampletest.presentation.viewmodel  String -com.example.sampletest.presentation.viewmodel  Unit -com.example.sampletest.presentation.viewmodel  asStateFlow -com.example.sampletest.presentation.viewmodel  	emptyList -com.example.sampletest.presentation.viewmodel  GetWeatherUseCase ;com.example.sampletest.presentation.viewmodel.NoteViewModel  Inject ;com.example.sampletest.presentation.viewmodel.NoteViewModel  List ;com.example.sampletest.presentation.viewmodel.NoteViewModel  MutableStateFlow ;com.example.sampletest.presentation.viewmodel.NoteViewModel  Note ;com.example.sampletest.presentation.viewmodel.NoteViewModel  NoteDao ;com.example.sampletest.presentation.viewmodel.NoteViewModel  	StateFlow ;com.example.sampletest.presentation.viewmodel.NoteViewModel  String ;com.example.sampletest.presentation.viewmodel.NoteViewModel  Unit ;com.example.sampletest.presentation.viewmodel.NoteViewModel  _notes ;com.example.sampletest.presentation.viewmodel.NoteViewModel  _weather ;com.example.sampletest.presentation.viewmodel.NoteViewModel  asStateFlow ;com.example.sampletest.presentation.viewmodel.NoteViewModel  	emptyList ;com.example.sampletest.presentation.viewmodel.NoteViewModel  getASStateFlow ;com.example.sampletest.presentation.viewmodel.NoteViewModel  getAsStateFlow ;com.example.sampletest.presentation.viewmodel.NoteViewModel  getEMPTYList ;com.example.sampletest.presentation.viewmodel.NoteViewModel  getEmptyList ;com.example.sampletest.presentation.viewmodel.NoteViewModel  Boolean com.example.sampletest.ui.theme  DarkColorScheme com.example.sampletest.ui.theme  LightColorScheme com.example.sampletest.ui.theme  Pink40 com.example.sampletest.ui.theme  Pink80 com.example.sampletest.ui.theme  Purple40 com.example.sampletest.ui.theme  Purple80 com.example.sampletest.ui.theme  PurpleGrey40 com.example.sampletest.ui.theme  PurpleGrey80 com.example.sampletest.ui.theme  SampleTestTheme com.example.sampletest.ui.theme  
Typography com.example.sampletest.ui.theme  Unit com.example.sampletest.ui.theme  SerializedName com.google.gson.annotations  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  ExperimentalMaterial3Api 	java.lang  MutableStateFlow 	java.lang  Note 	java.lang  OnConflictStrategy 	java.lang  SingletonComponent 	java.lang  asStateFlow 	java.lang  	emptyList 	java.lang  Inject javax.inject  	Singleton javax.inject  Array kotlin  Boolean kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  Note kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  Result kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  arrayOf kotlin  asStateFlow kotlin  	emptyList kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  ExperimentalMaterial3Api kotlin.annotation  MutableStateFlow kotlin.annotation  Note kotlin.annotation  OnConflictStrategy kotlin.annotation  Result kotlin.annotation  SingletonComponent kotlin.annotation  asStateFlow kotlin.annotation  	emptyList kotlin.annotation  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  MutableStateFlow kotlin.collections  Note kotlin.collections  OnConflictStrategy kotlin.collections  Result kotlin.collections  SingletonComponent kotlin.collections  asStateFlow kotlin.collections  	emptyList kotlin.collections  ExperimentalMaterial3Api kotlin.comparisons  MutableStateFlow kotlin.comparisons  Note kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Result kotlin.comparisons  SingletonComponent kotlin.comparisons  asStateFlow kotlin.comparisons  	emptyList kotlin.comparisons  ExperimentalMaterial3Api 	kotlin.io  MutableStateFlow 	kotlin.io  Note 	kotlin.io  OnConflictStrategy 	kotlin.io  Result 	kotlin.io  SingletonComponent 	kotlin.io  asStateFlow 	kotlin.io  	emptyList 	kotlin.io  ExperimentalMaterial3Api 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  Note 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Result 
kotlin.jvm  SingletonComponent 
kotlin.jvm  asStateFlow 
kotlin.jvm  	emptyList 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  Note 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Result 
kotlin.ranges  SingletonComponent 
kotlin.ranges  asStateFlow 
kotlin.ranges  	emptyList 
kotlin.ranges  KClass kotlin.reflect  ExperimentalMaterial3Api kotlin.sequences  MutableStateFlow kotlin.sequences  Note kotlin.sequences  OnConflictStrategy kotlin.sequences  Result kotlin.sequences  SingletonComponent kotlin.sequences  asStateFlow kotlin.sequences  	emptyList kotlin.sequences  ExperimentalMaterial3Api kotlin.text  MutableStateFlow kotlin.text  Note kotlin.text  OnConflictStrategy kotlin.text  Result kotlin.text  SingletonComponent kotlin.text  asStateFlow kotlin.text  	emptyList kotlin.text  launch kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  GET retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             