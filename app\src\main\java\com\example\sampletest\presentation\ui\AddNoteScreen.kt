package com.example.sampletest.presentation.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.sampletest.presentation.viewmodel.NoteViewModel

@Composable
fun AddNoteScreen (
    viewModel: NoteViewModel,
    onSaveDone: () -> Unit,
) {
    var title by remember { mutableStateOf("") }
    var content by remember { mutableStateOf("") }

    Column(Modifier.padding(16.dp)) {
        Text("Add Student")

        TextField(value = title, onValueChange = { title = it }, label = { Text("Title") })
        TextField(value = content, onValueChange = { content = it }, label = { Text("Content") })

        Spacer(Modifier.height(16.dp))
        Button(onClick = {
            viewModel.addNote(title, content) {
                onSaveDone();
            }
        }) {
            Text("Submit")
        }

        Spacer(Modifier.height(16.dp))
        Button(onClick = {
            viewModel.addNote(title, content) {
                onSaveDone();
            }
        }) {
            Text("Attach Weather Info")
        }
    }
}