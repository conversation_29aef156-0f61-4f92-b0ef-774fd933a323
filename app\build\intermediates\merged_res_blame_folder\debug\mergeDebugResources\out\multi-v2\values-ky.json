{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0b18247d773b7df6889ab954d02f2eb2\\transformed\\foundation-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,242", "endColumns": "87,98,103", "endOffsets": "138,237,341"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9041,9140", "endColumns": "87,98,103", "endOffsets": "188,9135,9239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da6d6308bd4bc683e79b06fa65febb61\\transformed\\core-1.17.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "193,293,395,498,605,707,811,8675", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "288,390,493,600,702,806,917,8771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89da252ff4a6a2b1f71aa9984a54919\\transformed\\material3-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4718,4804,4889,5001,5090,5174,5274,5375,5471,5568,5655,5766,5865,5965,6113,6203,6322", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4713,4799,4884,4996,5085,5169,5269,5370,5466,5563,5650,5761,5860,5960,6108,6198,6317,6425"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1574,1697,1814,1928,2053,2153,2251,2366,2502,2643,2799,2883,2981,3073,3170,3286,3405,3508,3644,3778,3915,4090,4219,4336,4456,4577,4670,4768,4890,5027,5130,5255,5360,5494,5633,5742,5844,5920,6019,6123,6237,6323,6408,6520,6609,6693,6793,6894,6990,7087,7174,7285,7384,7484,7632,7722,7841", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "1692,1809,1923,2048,2148,2246,2361,2497,2638,2794,2878,2976,3068,3165,3281,3400,3503,3639,3773,3910,4085,4214,4331,4451,4572,4665,4763,4885,5022,5125,5250,5355,5489,5628,5737,5839,5915,6014,6118,6232,6318,6403,6515,6604,6688,6788,6889,6985,7082,7169,7280,7379,7479,7627,7717,7836,7944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\090be5d6fafb061bae3b6e996ff571b4\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,287,371,481,581,666,748,846,935,1020,1105,1192,1265,1352,1426,1499,1572,1651,1719", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "282,366,476,576,661,743,841,930,1015,1100,1187,1260,1347,1421,1494,1567,1646,1714,1832"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1015,1099,1209,1309,1394,1476,7949,8038,8123,8208,8295,8368,8455,8529,8602,8776,8855,8923", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "1010,1094,1204,1304,1389,1471,1569,8033,8118,8203,8290,8363,8450,8524,8597,8670,8850,8918,9036"}}]}]}