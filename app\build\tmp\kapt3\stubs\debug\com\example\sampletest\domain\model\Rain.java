package com.example.sampletest.domain.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u0019\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0007J\u0010\u0010\u000b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0007J&\u0010\f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\rJ\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u001a\u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\b\u001a\u0004\b\u0006\u0010\u0007R\u001a\u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\b\u001a\u0004\b\t\u0010\u0007\u00a8\u0006\u0015"}, d2 = {"Lcom/example/sampletest/domain/model/Rain;", "", "oneHour", "", "threeHour", "(Ljava/lang/Double;Ljava/lang/Double;)V", "getOneHour", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getThreeHour", "component1", "component2", "copy", "(Ljava/lang/Double;Ljava/lang/Double;)Lcom/example/sampletest/domain/model/Rain;", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class Rain {
    @com.google.gson.annotations.SerializedName(value = "1h")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double oneHour = null;
    @com.google.gson.annotations.SerializedName(value = "3h")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double threeHour = null;
    
    public Rain(@org.jetbrains.annotations.Nullable()
    java.lang.Double oneHour, @org.jetbrains.annotations.Nullable()
    java.lang.Double threeHour) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getOneHour() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getThreeHour() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Rain copy(@org.jetbrains.annotations.Nullable()
    java.lang.Double oneHour, @org.jetbrains.annotations.Nullable()
    java.lang.Double threeHour) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}