package com.example.sampletest.domain.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b0\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001B\u0089\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\b\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u0012\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\u0006\u0010\u0015\u001a\u00020\u0016\u0012\u0006\u0010\u0017\u001a\u00020\u0018\u0012\u0006\u0010\u0019\u001a\u00020\f\u0012\u0006\u0010\u001a\u001a\u00020\f\u0012\u0006\u0010\u001b\u001a\u00020\b\u0012\u0006\u0010\u001c\u001a\u00020\f\u00a2\u0006\u0002\u0010\u001dJ\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\t\u00109\u001a\u00020\u0016H\u00c6\u0003J\t\u0010:\u001a\u00020\u0018H\u00c6\u0003J\t\u0010;\u001a\u00020\fH\u00c6\u0003J\t\u0010<\u001a\u00020\fH\u00c6\u0003J\t\u0010=\u001a\u00020\bH\u00c6\u0003J\t\u0010>\u001a\u00020\fH\u00c6\u0003J\u000f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010@\u001a\u00020\bH\u00c6\u0003J\t\u0010A\u001a\u00020\nH\u00c6\u0003J\t\u0010B\u001a\u00020\fH\u00c6\u0003J\u000b\u0010C\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u0010D\u001a\u00020\u0010H\u00c6\u0003J\u000b\u0010E\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\u000b\u0010F\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003J\u00ab\u0001\u0010G\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u00182\b\b\u0002\u0010\u0019\u001a\u00020\f2\b\b\u0002\u0010\u001a\u001a\u00020\f2\b\b\u0002\u0010\u001b\u001a\u00020\b2\b\b\u0002\u0010\u001c\u001a\u00020\fH\u00c6\u0001J\u0013\u0010H\u001a\u00020I2\b\u0010J\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010K\u001a\u00020\fH\u00d6\u0001J\t\u0010L\u001a\u00020\bH\u00d6\u0001R\u0016\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0016\u0010\u001a\u001a\u00020\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0016\u0010\u001b\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001fR\u0016\u0010\u000f\u001a\u00020\u00108\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0016\u0010\t\u001a\u00020\n8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0018\u0010\u0011\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0016\u0010\u001c\u001a\u00020\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010!R\u0018\u0010\u0013\u001a\u0004\u0018\u00010\u00148\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0016\u0010\u0017\u001a\u00020\u00188\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0016\u0010\u0015\u001a\u00020\u00168\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0016\u0010\u0019\u001a\u00020\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010!R\u0016\u0010\u000b\u001a\u00020\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010!R\u001c\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105R\u0018\u0010\r\u001a\u0004\u0018\u00010\u000e8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107\u00a8\u0006M"}, d2 = {"Lcom/example/sampletest/domain/model/WeatherResponse;", "", "coordinates", "Lcom/example/sampletest/domain/model/Coordinates;", "weather", "", "Lcom/example/sampletest/domain/model/Weather;", "base", "", "main", "Lcom/example/sampletest/domain/model/MainWeatherData;", "visibility", "", "wind", "Lcom/example/sampletest/domain/model/Wind;", "clouds", "Lcom/example/sampletest/domain/model/Clouds;", "rain", "Lcom/example/sampletest/domain/model/Rain;", "snow", "Lcom/example/sampletest/domain/model/Snow;", "timestamp", "", "sys", "Lcom/example/sampletest/domain/model/Sys;", "timezone", "cityId", "cityName", "responseCode", "(Lcom/example/sampletest/domain/model/Coordinates;Ljava/util/List;Ljava/lang/String;Lcom/example/sampletest/domain/model/MainWeatherData;ILcom/example/sampletest/domain/model/Wind;Lcom/example/sampletest/domain/model/Clouds;Lcom/example/sampletest/domain/model/Rain;Lcom/example/sampletest/domain/model/Snow;JLcom/example/sampletest/domain/model/Sys;IILjava/lang/String;I)V", "getBase", "()Ljava/lang/String;", "getCityId", "()I", "getCityName", "getClouds", "()Lcom/example/sampletest/domain/model/Clouds;", "getCoordinates", "()Lcom/example/sampletest/domain/model/Coordinates;", "getMain", "()Lcom/example/sampletest/domain/model/MainWeatherData;", "getRain", "()Lcom/example/sampletest/domain/model/Rain;", "getResponseCode", "getSnow", "()Lcom/example/sampletest/domain/model/Snow;", "getSys", "()Lcom/example/sampletest/domain/model/Sys;", "getTimestamp", "()J", "getTimezone", "getVisibility", "getWeather", "()Ljava/util/List;", "getWind", "()Lcom/example/sampletest/domain/model/Wind;", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class WeatherResponse {
    @com.google.gson.annotations.SerializedName(value = "coord")
    @org.jetbrains.annotations.NotNull()
    private final com.example.sampletest.domain.model.Coordinates coordinates = null;
    @com.google.gson.annotations.SerializedName(value = "weather")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.sampletest.domain.model.Weather> weather = null;
    @com.google.gson.annotations.SerializedName(value = "base")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String base = null;
    @com.google.gson.annotations.SerializedName(value = "main")
    @org.jetbrains.annotations.NotNull()
    private final com.example.sampletest.domain.model.MainWeatherData main = null;
    @com.google.gson.annotations.SerializedName(value = "visibility")
    private final int visibility = 0;
    @com.google.gson.annotations.SerializedName(value = "wind")
    @org.jetbrains.annotations.Nullable()
    private final com.example.sampletest.domain.model.Wind wind = null;
    @com.google.gson.annotations.SerializedName(value = "clouds")
    @org.jetbrains.annotations.NotNull()
    private final com.example.sampletest.domain.model.Clouds clouds = null;
    @com.google.gson.annotations.SerializedName(value = "rain")
    @org.jetbrains.annotations.Nullable()
    private final com.example.sampletest.domain.model.Rain rain = null;
    @com.google.gson.annotations.SerializedName(value = "snow")
    @org.jetbrains.annotations.Nullable()
    private final com.example.sampletest.domain.model.Snow snow = null;
    @com.google.gson.annotations.SerializedName(value = "dt")
    private final long timestamp = 0L;
    @com.google.gson.annotations.SerializedName(value = "sys")
    @org.jetbrains.annotations.NotNull()
    private final com.example.sampletest.domain.model.Sys sys = null;
    @com.google.gson.annotations.SerializedName(value = "timezone")
    private final int timezone = 0;
    @com.google.gson.annotations.SerializedName(value = "id")
    private final int cityId = 0;
    @com.google.gson.annotations.SerializedName(value = "name")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String cityName = null;
    @com.google.gson.annotations.SerializedName(value = "cod")
    private final int responseCode = 0;
    
    public WeatherResponse(@org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.Coordinates coordinates, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sampletest.domain.model.Weather> weather, @org.jetbrains.annotations.NotNull()
    java.lang.String base, @org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.MainWeatherData main, int visibility, @org.jetbrains.annotations.Nullable()
    com.example.sampletest.domain.model.Wind wind, @org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.Clouds clouds, @org.jetbrains.annotations.Nullable()
    com.example.sampletest.domain.model.Rain rain, @org.jetbrains.annotations.Nullable()
    com.example.sampletest.domain.model.Snow snow, long timestamp, @org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.Sys sys, int timezone, int cityId, @org.jetbrains.annotations.NotNull()
    java.lang.String cityName, int responseCode) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Coordinates getCoordinates() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.sampletest.domain.model.Weather> getWeather() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.MainWeatherData getMain() {
        return null;
    }
    
    public final int getVisibility() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sampletest.domain.model.Wind getWind() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Clouds getClouds() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sampletest.domain.model.Rain getRain() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sampletest.domain.model.Snow getSnow() {
        return null;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Sys getSys() {
        return null;
    }
    
    public final int getTimezone() {
        return 0;
    }
    
    public final int getCityId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCityName() {
        return null;
    }
    
    public final int getResponseCode() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Coordinates component1() {
        return null;
    }
    
    public final long component10() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Sys component11() {
        return null;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    public final int component15() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.sampletest.domain.model.Weather> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.MainWeatherData component4() {
        return null;
    }
    
    public final int component5() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sampletest.domain.model.Wind component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.Clouds component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sampletest.domain.model.Rain component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sampletest.domain.model.Snow component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sampletest.domain.model.WeatherResponse copy(@org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.Coordinates coordinates, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sampletest.domain.model.Weather> weather, @org.jetbrains.annotations.NotNull()
    java.lang.String base, @org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.MainWeatherData main, int visibility, @org.jetbrains.annotations.Nullable()
    com.example.sampletest.domain.model.Wind wind, @org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.Clouds clouds, @org.jetbrains.annotations.Nullable()
    com.example.sampletest.domain.model.Rain rain, @org.jetbrains.annotations.Nullable()
    com.example.sampletest.domain.model.Snow snow, long timestamp, @org.jetbrains.annotations.NotNull()
    com.example.sampletest.domain.model.Sys sys, int timezone, int cityId, @org.jetbrains.annotations.NotNull()
    java.lang.String cityName, int responseCode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}