1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.sampletest"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:5:22-64
12
13    <permission
13-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.sampletest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.sampletest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:26:22-94
18
19    <application
19-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:7:5-28:19
20        android:name="com.example.sampletest.MyApp"
20-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:8:9-30
21        android:allowBackup="true"
21-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da6d6308bd4bc683e79b06fa65febb61\transformed\core-1.17.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:15:9-35
31        android:testOnly="true"
32        android:theme="@style/Theme.SampleTest" >
32-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:16:9-48
33        <activity
33-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:17:9-27:20
34            android:name="com.example.sampletest.MainActivity"
34-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:18:13-41
35            android:exported="true"
35-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:19:13-36
36            android:label="@string/app_name"
36-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:20:13-45
37            android:theme="@style/Theme.SampleTest" >
37-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:21:13-52
38            <intent-filter>
38-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:22:13-26:29
39                <action android:name="android.intent.action.MAIN" />
39-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:23:17-69
39-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:23:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:25:17-77
41-->D:\Driver_D\RMIT(Android)\SampleTest\app\src\main\AndroidManifest.xml:25:27-74
42            </intent-filter>
43        </activity>
44        <activity
44-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
45            android:name="androidx.compose.ui.tooling.PreviewActivity"
45-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:24:13-71
46            android:exported="true" />
46-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cbcfb60ed503bead18593ab3ed06ae\transformed\ui-tooling\AndroidManifest.xml:25:13-36
47        <activity
47-->[androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:23:9-26:79
48            android:name="androidx.activity.ComponentActivity"
48-->[androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:24:13-63
49            android:exported="true"
49-->[androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:25:13-36
50            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
50-->[androidx.compose.ui:ui-test-manifest:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\494b037a392c9f93d746a8a5674882d9\transformed\ui-test-manifest-1.9.0\AndroidManifest.xml:26:13-76
51
52        <provider
52-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.sampletest.androidx-startup"
54-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b71b09b9759d21ed57cbcff9da6217fd\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13f1f3d073485ebd50ff933266b7a514\transformed\lifecycle-process-2.9.3\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <service
67-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
68            android:name="androidx.room.MultiInstanceInvalidationService"
68-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
69            android:directBootAware="true"
69-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
70            android:exported="false" />
70-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\374c101a991f3deba858e929a316a5d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
71
72        <receiver
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
73            android:name="androidx.profileinstaller.ProfileInstallReceiver"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
74            android:directBootAware="false"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
75            android:enabled="true"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
76            android:exported="true"
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
77            android:permission="android.permission.DUMP" >
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
79                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
82                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
85                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
88                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad984ff19f450efa9f5bf64ada316e3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
89            </intent-filter>
90        </receiver>
91    </application>
92
93</manifest>
