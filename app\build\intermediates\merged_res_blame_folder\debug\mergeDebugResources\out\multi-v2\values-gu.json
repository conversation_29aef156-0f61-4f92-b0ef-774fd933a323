{"logs": [{"outputFile": "com.example.sampletest.app-mergeDebugResources-50:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f907e2130997849173537d35f867bc4c\\transformed\\foundation-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,149,234", "endColumns": "93,84,84", "endOffsets": "144,229,314"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8881,8966", "endColumns": "93,84,84", "endOffsets": "194,8961,9046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f87273081a2a1619056a564e79ed1c26\\transformed\\core-1.17.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,353,455,557,655,777", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "148,251,348,450,552,650,772,873"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "199,297,400,497,599,701,799,8522", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "292,395,492,594,696,794,916,8618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0356b8c03d17076269fa25c954e232f\\transformed\\material3-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,406,518,613,712,828,967,1087,1229,1314,1418,1512,1612,1726,1854,1963,2098,2230,2360,2539,2665,2787,2913,3048,3143,3239,3366,3496,3597,3702,3809,3944,4085,4194,4296,4371,4468,4564,4671,4756,4843,4941,5021,5105,5205,5308,5406,5506,5593,5699,5798,5901,6019,6099,6199", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "164,276,401,513,608,707,823,962,1082,1224,1309,1413,1507,1607,1721,1849,1958,2093,2225,2355,2534,2660,2782,2908,3043,3138,3234,3361,3491,3592,3697,3804,3939,4080,4189,4291,4366,4463,4559,4666,4751,4838,4936,5016,5100,5200,5303,5401,5501,5588,5694,5793,5896,6014,6094,6194,6288"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1561,1675,1787,1912,2024,2119,2218,2334,2473,2593,2735,2820,2924,3018,3118,3232,3360,3469,3604,3736,3866,4045,4171,4293,4419,4554,4649,4745,4872,5002,5103,5208,5315,5450,5591,5700,5802,5877,5974,6070,6177,6262,6349,6447,6527,6611,6711,6814,6912,7012,7099,7205,7304,7407,7525,7605,7705", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "1670,1782,1907,2019,2114,2213,2329,2468,2588,2730,2815,2919,3013,3113,3227,3355,3464,3599,3731,3861,4040,4166,4288,4414,4549,4644,4740,4867,4997,5098,5203,5310,5445,5586,5695,5797,5872,5969,6065,6172,6257,6344,6442,6522,6606,6706,6809,6907,7007,7094,7200,7299,7402,7520,7600,7700,7794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\391a5066ef22ceff677de3e645b947ad\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "200,292,374,467,566,653,739,840,927,1013,1096,1179,1254,1338,1413,1488,1563,1639,1705", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,83,74,74,74,75,65,115", "endOffsets": "287,369,462,561,648,734,835,922,1008,1091,1174,1249,1333,1408,1483,1558,1634,1700,1816"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,1013,1095,1188,1287,1374,1460,7799,7886,7972,8055,8138,8213,8297,8372,8447,8623,8699,8765", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,83,74,74,74,75,65,115", "endOffsets": "1008,1090,1183,1282,1369,1455,1556,7881,7967,8050,8133,8208,8292,8367,8442,8517,8694,8760,8876"}}]}]}